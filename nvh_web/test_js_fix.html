<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript修复验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/airtightness.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">JavaScript修复验证页面</h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            此页面用于验证气密性JavaScript文件是否正常加载，无重复声明错误。
        </div>

        <!-- 测试多选组件 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-check-square me-2"></i>多选组件测试</h5>
            </div>
            <div class="card-body">
                <div class="vehicle-multiselect" id="vehicle-multiselect">
                    <div class="multiselect-container">
                        <div class="multiselect-input-container">
                            <input type="text" class="form-control multiselect-input" placeholder="点击选择车型..." readonly>
                            <i class="fas fa-chevron-down multiselect-arrow"></i>
                        </div>
                        <div class="multiselect-dropdown">
                            <div class="multiselect-search">
                                <input type="text" class="form-control form-control-sm" placeholder="搜索车型...">
                            </div>
                            <div class="multiselect-options">
                                <!-- 动态加载选项 -->
                            </div>
                        </div>
                    </div>
                    <div class="selected-items mt-2">
                        <!-- 已选择的车型标签 -->
                    </div>
                </div>
                <button type="button" class="btn btn-primary mt-3" id="generate-comparison-btn" disabled>
                    <i class="fas fa-chart-bar me-1"></i>生成对比表
                </button>
            </div>
        </div>

        <!-- 测试单选组件 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-list me-2"></i>单选组件测试</h5>
            </div>
            <div class="card-body">
                <select class="form-select" id="vehicle-select">
                    <option value="">请选择车型...</option>
                    <!-- 动态加载选项 -->
                </select>
                <button type="button" class="btn btn-info mt-3" id="view-images-btn" disabled>
                    <i class="fas fa-eye me-1"></i>查看图片
                </button>
            </div>
        </div>

        <!-- 状态显示 -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info me-2"></i>加载状态</h5>
            </div>
            <div class="card-body">
                <div id="status-info">
                    <p><strong>JavaScript加载状态:</strong> <span id="js-status" class="text-warning">检查中...</span></p>
                    <p><strong>API连接状态:</strong> <span id="api-status" class="text-warning">检查中...</span></p>
                    <p><strong>组件初始化状态:</strong> <span id="component-status" class="text-warning">检查中...</span></p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/airtightness.js"></script>
    <script>
        // 检查JavaScript加载状态
        document.addEventListener('DOMContentLoaded', function() {
            const jsStatus = document.getElementById('js-status');
            const apiStatus = document.getElementById('api-status');
            const componentStatus = document.getElementById('component-status');

            // 检查JavaScript是否正常加载
            if (window.AirtightnessModule) {
                jsStatus.textContent = '✅ 正常加载';
                jsStatus.className = 'text-success';
            } else {
                jsStatus.textContent = '❌ 加载失败';
                jsStatus.className = 'text-danger';
            }

            // 检查API连接
            fetch('/airtightness/api/vehicles')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        apiStatus.textContent = '✅ API连接正常';
                        apiStatus.className = 'text-success';
                    } else {
                        apiStatus.textContent = '❌ API返回错误';
                        apiStatus.className = 'text-danger';
                    }
                })
                .catch(error => {
                    apiStatus.textContent = '❌ API连接失败';
                    apiStatus.className = 'text-danger';
                });

            // 检查组件初始化
            setTimeout(() => {
                const multiSelect = document.getElementById('vehicle-multiselect');
                const singleSelect = document.getElementById('vehicle-select');
                
                if (multiSelect && singleSelect) {
                    // 检查是否有选项被加载
                    const hasMultiOptions = multiSelect.querySelector('.multiselect-options').children.length > 0;
                    const hasSingleOptions = singleSelect.children.length > 1;
                    
                    if (hasMultiOptions && hasSingleOptions) {
                        componentStatus.textContent = '✅ 组件初始化成功';
                        componentStatus.className = 'text-success';
                    } else {
                        componentStatus.textContent = '⚠️ 组件数据加载中...';
                        componentStatus.className = 'text-warning';
                    }
                } else {
                    componentStatus.textContent = '❌ 组件初始化失败';
                    componentStatus.className = 'text-danger';
                }
            }, 2000);
        });

        // 监听错误
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
            const jsStatus = document.getElementById('js-status');
            if (jsStatus) {
                jsStatus.textContent = '❌ JavaScript错误: ' + e.error.message;
                jsStatus.className = 'text-danger';
            }
        });
    </script>
</body>
</html>
