/* 基础样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    padding-top: 56px; /* 导航栏高度 */
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    background-color: #2c3e50 !important;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    transition: transform 0.3s ease;
    width: 16.66667%; /* col-lg-2 width */
}

/* 侧边栏收起状态 */
.sidebar.collapsed {
    transform: translateX(-100%);
}

/* 侧边栏切换按钮样式 */
#sidebarToggle {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

#sidebarToggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 侧边栏导航链接 */
.sidebar .nav-link {
    color: #ecf0f1;
    padding: 12px 20px;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    color: #3498db;
    background-color: rgba(52, 152, 219, 0.1);
    border-left-color: #3498db;
}

.sidebar .nav-link.active {
    color: #3498db;
    background-color: rgba(52, 152, 219, 0.2);
    border-left-color: #3498db;
    font-weight: 500;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-right: 10px;
}

/* 可折叠菜单样式 */
.sidebar .nav-link[data-bs-toggle="collapse"] {
    position: relative;
}

.sidebar .nav-link[data-bs-toggle="collapse"] .fa-chevron-down {
    transition: transform 0.3s ease;
    margin-left: auto !important;
}

.sidebar .nav-link[data-bs-toggle="collapse"]:not(.collapsed) .fa-chevron-down {
    transform: rotate(180deg);
}

/* 子菜单样式 */
.sidebar .collapse .nav-link {
    color: #bdc3c7;
    background-color: rgba(0, 0, 0, 0.1);
    border-left: 3px solid transparent;
}

.sidebar .collapse .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.2);
    color: #3498db;
    border-left-color: #3498db;
}

/* 主内容区域 */
main {
    padding-top: 20px;
    min-height: calc(100vh - 56px);
    background-color: #ffffff;
    transition: margin-left 0.3s ease, width 0.3s ease;
}

/* 侧边栏展开时的主内容区域 */
.sidebar-expanded main {
    margin-left: 16.66667%;
    width: calc(100% - 16.66667%);
}

/* 侧边栏收起时的主内容区域 */
.sidebar-collapsed main {
    margin-left: 0;
    width: 100%;
}

/* 卡片样式增强 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* 搜索表单样式 */
.search-form {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

/* 结果统计样式 */
.result-stats {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 10px 15px;
    margin-bottom: 15px;
    border-radius: 0 4px 4px 0;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .sidebar {
        position: relative;
        top: 0;
        height: auto;
        width: 100%;
        transform: none;
    }

    .sidebar.collapsed {
        display: none;
    }

    main {
        padding-top: 10px;
    }

    .sidebar-collapsed main {
        margin-left: 0;
        width: 100%;
    }
}

@media (min-width: 768px) {
    .sidebar {
        width: 25%; /* col-md-3 width */
    }
}

@media (min-width: 992px) {
    .sidebar {
        width: 16.66667%; /* col-lg-2 width */
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 消息提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
}

/* 表单控件样式 */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-info {
    border-left-color: #17a2b8;
}

.alert-success {
    border-left-color: #28a745;
}

.alert-warning {
    border-left-color: #ffc107;
}

.alert-danger {
    border-left-color: #dc3545;
}
