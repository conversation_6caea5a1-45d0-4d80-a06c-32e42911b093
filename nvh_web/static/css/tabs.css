/* 标签页系统样式 */

/* 标签栏容器 */
.tab-bar-container {
    background-color: #ffffff;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 99;
}

/* 标签栏 */
.tab-bar {
    display: flex;
    align-items: center;
    padding: 0;
    margin: 0;
    list-style: none;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.tab-bar::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 标签页项 */
.tab-item {
    display: inline-flex;
    align-items: center;
    min-width: 120px;
    max-width: 200px;
    height: 40px;
    padding: 0 12px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    margin-right: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    user-select: none;
}

.tab-item:hover {
    background-color: #e9ecef;
}

.tab-item.active {
    background-color: #ffffff;
    border-color: #007bff;
    border-bottom: 2px solid #ffffff;
    z-index: 1;
    margin-bottom: -1px;
}

/* 标签页标题 */
.tab-title {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
}

.tab-item.active .tab-title {
    color: #007bff;
}

/* 标签页图标 */
.tab-icon {
    margin-right: 6px;
    font-size: 12px;
    color: #6c757d;
}

.tab-item.active .tab-icon {
    color: #007bff;
}

/* 关闭按钮 */
.tab-close {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    color: #6c757d;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0;
}

.tab-item:hover .tab-close {
    opacity: 1;
}

.tab-close:hover {
    background-color: #dc3545;
    color: #ffffff;
}

/* 首页标签特殊样式（不可关闭） */
.tab-item.home-tab .tab-close {
    display: none;
}

/* 标签内容区域 */
.tab-content-container {
    position: relative;
    min-height: calc(100vh - 136px); /* 减去导航栏和标签栏高度 */
    background-color: #ffffff;
}

.tab-content {
    display: none;
    padding: 20px;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 标签栏滚动按钮 */
.tab-scroll-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-scroll-btn:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
}

.tab-scroll-left {
    left: 10px;
}

.tab-scroll-right {
    right: 10px;
}

/* 新标签按钮 */
.new-tab-btn {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background-color: #007bff;
    border: none;
    border-radius: 50%;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.new-tab-btn:hover {
    background-color: #0056b3;
    transform: translateY(-50%) scale(1.1);
}

/* 标签页右键菜单 */
.tab-context-menu {
    position: fixed;
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 4px 0;
    min-width: 120px;
    z-index: 1000;
    display: none;
}

.tab-context-menu-item {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    color: #495057;
    transition: background-color 0.2s ease;
}

.tab-context-menu-item:hover {
    background-color: #f8f9fa;
}

.tab-context-menu-item.disabled {
    color: #6c757d;
    cursor: not-allowed;
}

.tab-context-menu-item.disabled:hover {
    background-color: transparent;
}

/* 标签页加载状态 */
.tab-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.tab-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .tab-item {
        min-width: 100px;
        max-width: 150px;
        padding: 0 8px;
    }
    
    .tab-title {
        font-size: 13px;
    }
    
    .tab-scroll-btn,
    .new-tab-btn {
        width: 28px;
        height: 28px;
    }
    
    .tab-content {
        padding: 15px;
    }
}

/* 标签页拖拽样式 */
.tab-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.tab-item.drag-over {
    border-left: 3px solid #007bff;
}

/* 标签页动画 */
.tab-item.closing {
    animation: tabClose 0.3s ease forwards;
}

@keyframes tabClose {
    to {
        width: 0;
        padding: 0;
        margin: 0;
        opacity: 0;
    }
}

.tab-item.opening {
    animation: tabOpen 0.3s ease forwards;
}

@keyframes tabOpen {
    from {
        width: 0;
        padding: 0;
        margin: 0;
        opacity: 0;
    }
    to {
        width: auto;
        padding: 0 12px;
        margin-right: 2px;
        opacity: 1;
    }
}

/* 标签页状态指示器 */
.tab-status {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #28a745;
}

.tab-status.modified {
    background-color: #ffc107;
}

.tab-status.error {
    background-color: #dc3545;
}

/* 标签页工具栏 */
.tab-toolbar {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: 5px;
}

.tab-toolbar-btn {
    width: 24px;
    height: 24px;
    border: none;
    background-color: transparent;
    border-radius: 4px;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-toolbar-btn:hover {
    background-color: #f8f9fa;
    color: #495057;
}
