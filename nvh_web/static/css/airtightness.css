/* 气密性模块样式 */

/* 多选框样式 */
.vehicle-multiselect {
    position: relative;
}

.multiselect-container {
    position: relative;
}

.multiselect-input-container {
    position: relative;
    cursor: pointer;
}

.multiselect-input {
    cursor: pointer;
    padding-right: 40px;
}

.multiselect-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    transition: transform 0.2s;
}

.multiselect-container.open .multiselect-arrow {
    transform: translateY(-50%) rotate(180deg);
}

.multiselect-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.multiselect-container.open .multiselect-dropdown {
    display: block;
}

.multiselect-search {
    padding: 8px;
    border-bottom: 1px solid #e9ecef;
}

.multiselect-options {
    max-height: 200px;
    overflow-y: auto;
}

.multiselect-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    align-items: center;
}

.multiselect-option:hover {
    background-color: #f8f9fa;
}

.multiselect-option.selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

.multiselect-option input[type="checkbox"] {
    margin-right: 8px;
}

.selected-items {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.selected-item {
    display: inline-flex;
    align-items: center;
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
}

.selected-item .remove-btn {
    margin-left: 6px;
    cursor: pointer;
    color: #1976d2;
    font-weight: bold;
}

.selected-item .remove-btn:hover {
    color: #d32f2f;
}

/* 优化布局对齐 */
.vehicle-multiselect {
    min-height: 38px; /* 确保与按钮高度一致 */
}

.multiselect-input-container {
    min-height: 38px;
}

/* 确保按钮与输入框顶部对齐 */
.d-flex.align-items-start .btn {
    margin-top: 0;
}

/* 选择项标签容器固定高度，避免影响布局 */
.selected-items {
    min-height: 32px; /* 为标签预留空间 */
    margin-top: 8px !important;
}

/* 优化缩短宽度后的显示效果 */
.vehicle-multiselect .multiselect-input {
    min-width: 200px; /* 确保最小宽度能显示车型名称 */
}

.vehicle-multiselect .selected-items .selected-item {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 单选框样式优化 */
#vehicle-select {
    min-width: 200px; /* 确保单选框最小宽度 */
}

/* 对比表格样式 - 参考模态数据页面样式 */
#comparison-table {
    font-size: 0.875rem;
}

#comparison-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    border: 1px solid #dee2e6;
    padding: 0.75rem 0.5rem;
}

#comparison-table td {
    text-align: center;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 0.5rem;
}

#comparison-table .category-cell {
    font-weight: 600;
    background-color: #f8f9fa;
    text-align: left;
    padding-left: 1rem;
}

#comparison-table .subcategory-cell {
    text-align: left;
    padding-left: 2rem;
    font-weight: 500;
    color: #495057;
}

#comparison-table .value-cell {
    font-weight: 500;
    color: #212529;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

#comparison-table .no-data {
    color: #6c757d;
    font-style: italic;
    font-weight: normal;
}

/* 测试信息表格样式 */
#test-info-table {
    margin-top: 20px;
    font-size: 0.875rem;
}

#test-info-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 0.75rem 0.5rem;
}

#test-info-table td {
    text-align: center;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    font-weight: 500;
    color: #212529;
}

/* 图片展示样式 */
.image-container {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    overflow: hidden;
}

.image-container img {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
    cursor: pointer;
    transition: transform 0.2s;
}

.image-container img:hover {
    transform: scale(1.05);
}

.no-image-placeholder {
    text-align: center;
    color: #6c757d;
}

.no-image-placeholder i {
    display: block;
    margin-bottom: 10px;
}

/* 加载状态样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* 表格边框和间距优化 */
.table-responsive {
    border-radius: 0.5rem;
}

#comparison-table,
#test-info-table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.375rem;
    overflow: hidden;
}

/* 表格头部样式统一 */
.table-dark th {
    background-color: #495057 !important;
    color: white !important;
    font-weight: 600;
    border-color: #495057 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .multiselect-dropdown {
        position: fixed;
        top: auto;
        left: 10px;
        right: 10px;
        max-height: 50vh;
    }

    #comparison-table {
        font-size: 0.75rem;
    }

    #comparison-table th,
    #comparison-table td {
        padding: 0.25rem;
    }

    #test-info-table {
        font-size: 0.75rem;
    }

    #test-info-table th,
    #test-info-table td {
        padding: 0.25rem;
    }
    
    .image-container {
        min-height: 150px;
    }
    
    .image-container img {
        max-height: 200px;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表格行悬停效果 */
#comparison-table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
}

/* 按钮组样式 */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 生成对比表按钮样式优化 */
#generate-comparison-btn {
    height: 38px; /* 与form-control高度一致 */
    font-weight: 500;
    min-width: 120px;
}

/* 表单标签样式统一 */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}
