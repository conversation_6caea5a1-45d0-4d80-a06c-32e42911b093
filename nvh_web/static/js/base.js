/**
 * 基础JavaScript功能
 */

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * 初始化应用
 */
function initializeApp() {
    // 初始化侧边栏
    initializeSidebar();

    // 初始化侧边栏切换功能
    initializeSidebarToggle();

    // 初始化键盘快捷键
    initializeKeyboardShortcuts();

    // 初始化用户信息
    initializeUserInfo();

    // 设置当前页面的导航高亮
    highlightCurrentNav();
}

/**
 * 初始化侧边栏
 */
function initializeSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;

    // 处理可折叠菜单
    const collapseLinks = sidebar.querySelectorAll('[data-bs-toggle="collapse"]');
    collapseLinks.forEach(link => {
        const target = document.querySelector(link.getAttribute('data-bs-target'));
        if (target) {
            // 监听折叠状态变化
            target.addEventListener('show.bs.collapse', () => {
                link.classList.remove('collapsed');
            });
            
            target.addEventListener('hide.bs.collapse', () => {
                link.classList.add('collapsed');
            });
        }
    });

    // 处理菜单项点击
    const navLinks = sidebar.querySelectorAll('.nav-link:not([data-bs-toggle])');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 如果是标签页模式，阻止默认跳转
            if (window.tabManager) {
                e.preventDefault();
                return;
            }
            
            // 移除其他链接的active状态
            navLinks.forEach(l => l.classList.remove('active'));
            // 添加当前链接的active状态
            this.classList.add('active');
        });
    });
}

/**
 * 初始化侧边栏切换功能
 */
function initializeSidebarToggle() {
    const toggleBtn = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    if (!toggleBtn || !sidebar) return;

    toggleBtn.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        document.body.classList.toggle('sidebar-collapsed');
        
        // 保存侧边栏状态到localStorage
        const isCollapsed = sidebar.classList.contains('collapsed');
        localStorage.setItem('sidebarCollapsed', isCollapsed);
    });

    // 恢复侧边栏状态
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState === 'true') {
        sidebar.classList.add('collapsed');
        document.body.classList.add('sidebar-collapsed');
    }
}

/**
 * 初始化键盘快捷键
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + B: 切换侧边栏
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            const toggleBtn = document.getElementById('sidebarToggle');
            if (toggleBtn) {
                toggleBtn.click();
            }
        }
        
        // Ctrl/Cmd + T: 新建标签页
        if ((e.ctrlKey || e.metaKey) && e.key === 't' && window.tabManager) {
            e.preventDefault();
            window.tabManager.createTab({
                title: '新标签页',
                icon: 'fas fa-file',
                content: '<div class="text-center py-5"><h4>新标签页</h4><p class="text-muted">请从左侧菜单选择功能</p></div>'
            });
        }
        
        // Ctrl/Cmd + W: 关闭当前标签页
        if ((e.ctrlKey || e.metaKey) && e.key === 'w' && window.tabManager) {
            e.preventDefault();
            if (window.tabManager.activeTabId && window.tabManager.activeTabId !== 'home') {
                window.tabManager.closeTab(window.tabManager.activeTabId);
            }
        }
        
        // Ctrl/Cmd + R: 刷新当前标签页
        if ((e.ctrlKey || e.metaKey) && e.key === 'r' && window.tabManager) {
            e.preventDefault();
            if (window.tabManager.activeTabId) {
                window.tabManager.refreshTab(window.tabManager.activeTabId);
            }
        }
    });
}

/**
 * 初始化用户信息
 */
function initializeUserInfo() {
    const userDropdown = document.querySelector('.navbar .dropdown-toggle');
    if (userDropdown) {
        // 可以在这里添加用户信息相关的初始化逻辑
        console.log('用户信息初始化完成');
    }
}

/**
 * 设置当前页面的导航高亮
 */
function highlightCurrentNav() {
    if (window.tabManager) {
        // 标签页模式下由TabManager处理导航高亮
        return;
    }
    
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.sidebar .nav-link[href]');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPath) {
            link.classList.add('active');
            
            // 如果是子菜单项，展开父菜单
            const parentCollapse = link.closest('.collapse');
            if (parentCollapse) {
                parentCollapse.classList.add('show');
                const parentLink = document.querySelector(`[data-bs-target="#${parentCollapse.id}"]`);
                if (parentLink) {
                    parentLink.classList.remove('collapsed');
                }
            }
        }
    });
}

/**
 * 显示即将推出提示
 */
function showComingSoon() {
    showMessage('该功能正在开发中，敬请期待！', 'info');
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 复制文本到剪贴板
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showMessage('已复制到剪贴板', 'success');
    } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showMessage('已复制到剪贴板', 'success');
        } catch (err) {
            showMessage('复制失败', 'error');
        }
        document.body.removeChild(textArea);
    }
}

/**
 * 下载文件
 */
function downloadFile(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * 获取URL参数
 */
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    const result = {};
    for (const [key, value] of params) {
        result[key] = value;
    }
    return result;
}

/**
 * 设置URL参数
 */
function setUrlParams(params) {
    const url = new URL(window.location);
    Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
            url.searchParams.set(key, params[key]);
        } else {
            url.searchParams.delete(key);
        }
    });
    window.history.replaceState({}, '', url);
}

/**
 * 滚动到元素
 */
function scrollToElement(element, offset = 0) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;
        
        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    }
}

/**
 * 检查元素是否在视口中
 */
function isElementInViewport(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (!element) return false;
    
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

/**
 * 初始化工具提示
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 初始化弹出框
 */
function initializePopovers() {
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * 全屏切换
 */
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
}

/**
 * 打印页面
 */
function printPage() {
    window.print();
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript错误:', e.error);
    // 可以在这里添加错误上报逻辑
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        console.log('页面隐藏');
    } else {
        console.log('页面显示');
    }
});

// 页面卸载前处理
window.addEventListener('beforeunload', function(e) {
    // 可以在这里添加数据保存逻辑
    // e.preventDefault();
    // e.returnValue = '';
});
