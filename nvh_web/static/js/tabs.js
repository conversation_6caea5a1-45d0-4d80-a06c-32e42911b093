/**
 * 标签页管理系统
 */
class TabManager {
    constructor() {
        this.tabs = new Map();
        this.activeTabId = null;
        this.tabCounter = 0;
        this.init();
    }

    /**
     * 初始化标签页管理器
     */
    init() {
        this.createTabBar();
        this.bindEvents();
        
        // 创建首页标签
        this.createTab({
            id: 'home',
            title: '首页',
            icon: 'fas fa-home',
            url: '/',
            closable: false,
            content: this.getHomeContent()
        });
        
        this.activateTab('home');
    }

    /**
     * 创建标签栏
     */
    createTabBar() {
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) return;

        const tabBarHtml = `
            <div class="tab-bar-container">
                <ul class="tab-bar" id="tabBar"></ul>
                <div class="tab-toolbar">
                    <button class="tab-toolbar-btn" id="newTabBtn" title="新建标签">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="tab-content-container" id="tabContentContainer"></div>
        `;

        mainContent.innerHTML = tabBarHtml;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 新建标签按钮
        document.getElementById('newTabBtn')?.addEventListener('click', () => {
            this.createTab({
                title: '新标签页',
                icon: 'fas fa-file',
                content: '<div class="text-center py-5"><h4>新标签页</h4><p class="text-muted">请从左侧菜单选择功能</p></div>'
            });
        });

        // 侧边栏菜单点击事件
        document.addEventListener('click', (e) => {
            const link = e.target.closest('.sidebar .nav-link[href]');
            if (link && !link.hasAttribute('data-bs-toggle')) {
                e.preventDefault();
                const href = link.getAttribute('href');
                const title = link.textContent.trim();
                const icon = link.querySelector('i')?.className || 'fas fa-file';
                
                this.openPage(href, title, icon);
            }
        });

        // 右键菜单
        document.addEventListener('contextmenu', (e) => {
            const tabItem = e.target.closest('.tab-item');
            if (tabItem) {
                e.preventDefault();
                this.showContextMenu(e, tabItem.dataset.tabId);
            }
        });

        // 点击其他地方关闭右键菜单
        document.addEventListener('click', () => {
            this.hideContextMenu();
        });
    }

    /**
     * 创建标签页
     */
    createTab(options) {
        const {
            id = `tab-${++this.tabCounter}`,
            title = '新标签页',
            icon = 'fas fa-file',
            url = '',
            closable = true,
            content = ''
        } = options;

        // 如果标签已存在，直接激活
        if (this.tabs.has(id)) {
            this.activateTab(id);
            return id;
        }

        // 创建标签页数据
        const tab = {
            id,
            title,
            icon,
            url,
            closable,
            content,
            element: null,
            contentElement: null
        };

        // 创建标签页元素
        this.createTabElement(tab);
        this.createTabContent(tab);

        // 保存标签页
        this.tabs.set(id, tab);

        // 激活新标签页
        this.activateTab(id);

        return id;
    }

    /**
     * 创建标签页元素
     */
    createTabElement(tab) {
        const tabBar = document.getElementById('tabBar');
        if (!tabBar) return;

        const tabElement = document.createElement('li');
        tabElement.className = 'tab-item';
        tabElement.dataset.tabId = tab.id;
        
        if (!tab.closable) {
            tabElement.classList.add('home-tab');
        }

        tabElement.innerHTML = `
            <i class="${tab.icon} tab-icon"></i>
            <span class="tab-title" title="${tab.title}">${tab.title}</span>
            ${tab.closable ? '<button class="tab-close" title="关闭"><i class="fas fa-times"></i></button>' : ''}
        `;

        // 绑定事件
        tabElement.addEventListener('click', (e) => {
            if (!e.target.closest('.tab-close')) {
                this.activateTab(tab.id);
            }
        });

        if (tab.closable) {
            const closeBtn = tabElement.querySelector('.tab-close');
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.closeTab(tab.id);
            });
        }

        tabBar.appendChild(tabElement);
        tab.element = tabElement;
    }

    /**
     * 创建标签页内容
     */
    createTabContent(tab) {
        const container = document.getElementById('tabContentContainer');
        if (!container) return;

        const contentElement = document.createElement('div');
        contentElement.className = 'tab-content';
        contentElement.dataset.tabId = tab.id;
        contentElement.innerHTML = tab.content;

        container.appendChild(contentElement);
        tab.contentElement = contentElement;
    }

    /**
     * 激活标签页
     */
    activateTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) return;

        // 取消所有标签页的激活状态
        document.querySelectorAll('.tab-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // 激活当前标签页
        tab.element.classList.add('active');
        tab.contentElement.classList.add('active');

        // 更新侧边栏导航高亮
        this.updateSidebarHighlight(tab.url);

        this.activeTabId = tabId;
    }

    /**
     * 关闭标签页
     */
    closeTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab || !tab.closable) return;

        // 如果关闭的是当前激活的标签页，需要激活其他标签页
        if (this.activeTabId === tabId) {
            const tabIds = Array.from(this.tabs.keys());
            const currentIndex = tabIds.indexOf(tabId);
            let nextTabId = null;

            // 优先激活右侧标签页，如果没有则激活左侧
            if (currentIndex < tabIds.length - 1) {
                nextTabId = tabIds[currentIndex + 1];
            } else if (currentIndex > 0) {
                nextTabId = tabIds[currentIndex - 1];
            }

            if (nextTabId) {
                this.activateTab(nextTabId);
            }
        }

        // 移除DOM元素
        tab.element.remove();
        tab.contentElement.remove();

        // 从Map中删除
        this.tabs.delete(tabId);
    }

    /**
     * 打开页面
     */
    async openPage(url, title, icon) {
        const tabId = this.getTabIdByUrl(url);

        // 如果标签页已存在，直接激活
        if (tabId && this.tabs.has(tabId)) {
            this.activateTab(tabId);
            return;
        }

        // 创建新标签页
        const newTabId = this.createTab({
            title,
            icon,
            url,
            content: '<div class="tab-loading"><div class="tab-loading-spinner"></div></div>'
        });

        // 加载页面内容
        try {
            // 对于特定页面，使用专门的内容路由
            let fetchUrl = url;
            if (url === '/business_center') {
                fetchUrl = '/business_center_content';
            } else if (url === '/airtightness/comparison') {
                fetchUrl = '/airtightness/comparison_content';
            } else if (url === '/airtightness/images') {
                fetchUrl = '/airtightness/images_content';
            }

            const response = await fetch(fetchUrl);
            const html = await response.text();

            // 提取页面内容（去掉base.html的外层结构）
            const content = this.extractPageContent(html);

            const tab = this.tabs.get(newTabId);
            if (tab) {
                tab.contentElement.innerHTML = content;

                // 执行页面中的脚本
                this.executePageScripts(tab.contentElement);
            }
        } catch (error) {
            console.error('加载页面失败:', error);
            const tab = this.tabs.get(newTabId);
            if (tab) {
                tab.contentElement.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h4>页面加载失败</h4>
                        <p class="text-muted">${error.message}</p>
                        <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                    </div>
                `;
            }
        }
    }

    /**
     * 根据URL获取标签页ID
     */
    getTabIdByUrl(url) {
        for (const [id, tab] of this.tabs) {
            if (tab.url === url) {
                return id;
            }
        }
        return null;
    }

    /**
     * 提取页面内容
     */
    extractPageContent(html) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 首先尝试提取 main 元素的内容
        const main = doc.querySelector('main');
        if (main && main.innerHTML.trim()) {
            return main.innerHTML;
        }

        // 如果 main 为空，尝试提取 body 中的内容（排除导航栏和侧边栏）
        const body = doc.querySelector('body');
        if (body) {
            // 移除导航栏、侧边栏等不需要的元素
            const elementsToRemove = body.querySelectorAll('nav.navbar, nav.sidebar, .toast-container, script');
            elementsToRemove.forEach(el => el.remove());

            // 查找主要内容区域
            const container = body.querySelector('.container-fluid .row');
            if (container) {
                const mainContent = container.querySelector('main') || container.querySelector('.col-md-9, .col-lg-10');
                if (mainContent) {
                    return mainContent.innerHTML;
                }
            }
        }

        // 最后的降级方案
        return html;
    }

    /**
     * 执行页面脚本
     */
    executePageScripts(container) {
        const scripts = container.querySelectorAll('script');
        scripts.forEach(script => {
            const newScript = document.createElement('script');
            if (script.src) {
                newScript.src = script.src;
            } else {
                newScript.textContent = script.textContent;
            }
            document.head.appendChild(newScript);
            document.head.removeChild(newScript);
        });
    }

    /**
     * 更新侧边栏导航高亮
     */
    updateSidebarHighlight(url) {
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === url) {
                link.classList.add('active');
            }
        });
    }

    /**
     * 显示右键菜单
     */
    showContextMenu(event, tabId) {
        this.hideContextMenu();

        const tab = this.tabs.get(tabId);
        if (!tab) return;

        const menu = document.createElement('div');
        menu.className = 'tab-context-menu';
        menu.innerHTML = `
            <div class="tab-context-menu-item" data-action="refresh">刷新</div>
            <div class="tab-context-menu-item ${!tab.closable ? 'disabled' : ''}" data-action="close">关闭</div>
            <div class="tab-context-menu-item" data-action="close-others">关闭其他</div>
            <div class="tab-context-menu-item" data-action="close-all">关闭所有</div>
        `;

        menu.style.left = event.pageX + 'px';
        menu.style.top = event.pageY + 'px';
        document.body.appendChild(menu);

        // 绑定菜单项点击事件
        menu.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action && !e.target.classList.contains('disabled')) {
                this.handleContextMenuAction(action, tabId);
            }
            this.hideContextMenu();
        });

        this.contextMenu = menu;
    }

    /**
     * 隐藏右键菜单
     */
    hideContextMenu() {
        if (this.contextMenu) {
            this.contextMenu.remove();
            this.contextMenu = null;
        }
    }

    /**
     * 处理右键菜单操作
     */
    handleContextMenuAction(action, tabId) {
        switch (action) {
            case 'refresh':
                this.refreshTab(tabId);
                break;
            case 'close':
                this.closeTab(tabId);
                break;
            case 'close-others':
                this.closeOtherTabs(tabId);
                break;
            case 'close-all':
                this.closeAllTabs();
                break;
        }
    }

    /**
     * 刷新标签页
     */
    refreshTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab || !tab.url) return;

        this.openPage(tab.url, tab.title, tab.icon);
    }

    /**
     * 关闭其他标签页
     */
    closeOtherTabs(keepTabId) {
        const tabIds = Array.from(this.tabs.keys());
        tabIds.forEach(id => {
            if (id !== keepTabId) {
                this.closeTab(id);
            }
        });
    }

    /**
     * 关闭所有标签页
     */
    closeAllTabs() {
        const tabIds = Array.from(this.tabs.keys());
        tabIds.forEach(id => {
            this.closeTab(id);
        });
    }

    /**
     * 获取首页内容
     */
    getHomeContent() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">欢迎使用NVH数据管理系统</h1>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="welcome-section text-center py-5">
                        <div class="mb-5">
                            <i class="fas fa-chart-line fa-4x text-primary mb-4"></i>
                            <h2 class="mb-4">NVH数据管理系统</h2>
                            <p class="lead text-muted mb-4">专业的汽车NVH数据管理与分析平台</p>
                        </div>

                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="card border-primary">
                                    <div class="card-body p-4">
                                        <h5 class="card-title text-primary mb-3">
                                            <i class="fas fa-briefcase me-2"></i>开始使用
                                        </h5>
                                        <p class="card-text mb-4">点击左侧菜单的"业务中心"，选择您需要的功能模块</p>
                                        <button class="btn btn-primary btn-lg" onclick="window.tabManager.openPage('/business_center', '业务中心', 'fas fa-briefcase')">
                                            <i class="fas fa-briefcase me-2"></i>进入业务中心
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-5">
                            <div class="col-md-4 mb-3">
                                <div class="feature-item p-3">
                                    <i class="fas fa-wave-square fa-2x text-info mb-2"></i>
                                    <h6>模态分析</h6>
                                    <small class="text-muted">专业的模态数据管理</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="feature-item p-3">
                                    <i class="fas fa-volume-up fa-2x text-success mb-2"></i>
                                    <h6>声学测试</h6>
                                    <small class="text-muted">全面的声学数据分析</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="feature-item p-3">
                                    <i class="fas fa-tachometer-alt fa-2x text-warning mb-2"></i>
                                    <h6>气密性测试</h6>
                                    <small class="text-muted">精确的气密性检测</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <style>
            .feature-item {
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .feature-item:hover {
                background-color: #f8f9fa;
                transform: translateY(-2px);
            }
            </style>
        `;
    }
}

// 全局标签页管理器实例
let tabManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 只在已登录状态下初始化标签页管理器
    if (document.querySelector('.sidebar')) {
        tabManager = new TabManager();
        window.tabManager = tabManager; // 暴露到全局作用域
    }
});
