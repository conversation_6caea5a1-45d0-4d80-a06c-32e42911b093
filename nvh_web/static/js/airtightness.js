/**
 * 气密性模块JavaScript
 * 版本: 2.0 - 修复变量冲突问题
 * 更新时间: 2025-08-23
 */

// 防止重复加载
if (window.AirtightnessModule) {
    console.warn('气密性模块已加载，跳过重复加载');
} else {
    window.AirtightnessModule = true;

// 工具函数
const utils = {
    // 显示消息
    showMessage: function(message, type = 'info') {
        // 创建消息元素
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    },
    
    // 发送请求
    request: async function(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.code !== 200) {
                throw new Error(data.message || '请求失败');
            }
            
            return data;
        } catch (error) {
            console.error('Request failed:', error);
            throw error;
        }
    }
};

// 多选框组件
class VehicleMultiSelect {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.selectedVehicles = [];
        this.allVehicles = [];
        this.isOpen = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadVehicles();
    }
    
    setupEventListeners() {
        const inputContainer = this.container.querySelector('.multiselect-input-container');
        const searchInput = this.container.querySelector('.multiselect-search input');
        
        // 点击输入框切换下拉菜单
        inputContainer.addEventListener('click', () => {
            this.toggle();
        });
        
        // 搜索功能
        searchInput.addEventListener('input', (e) => {
            this.filterOptions(e.target.value);
        });
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.close();
            }
        });
    }
    
    async loadVehicles() {
        try {
            const response = await utils.request('/airtightness/api/vehicles');
            this.allVehicles = response.data || [];
            this.renderOptions();
        } catch (error) {
            utils.showMessage('加载车型列表失败: ' + error.message, 'danger');
        }
    }
    
    renderOptions() {
        const optionsContainer = this.container.querySelector('.multiselect-options');
        optionsContainer.innerHTML = '';
        
        this.allVehicles.forEach(vehicle => {
            const option = document.createElement('div');
            option.className = 'multiselect-option';
            option.innerHTML = `
                <input type="checkbox" value="${vehicle.id}" ${this.selectedVehicles.includes(vehicle.id) ? 'checked' : ''}>
                <span>${vehicle.name}</span>
            `;
            
            option.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    const checkbox = option.querySelector('input[type="checkbox"]');
                    checkbox.checked = !checkbox.checked;
                }
                this.handleOptionClick(vehicle.id);
            });
            
            optionsContainer.appendChild(option);
        });
    }
    
    handleOptionClick(vehicleId) {
        const index = this.selectedVehicles.indexOf(vehicleId);
        if (index > -1) {
            this.selectedVehicles.splice(index, 1);
        } else {
            this.selectedVehicles.push(vehicleId);
        }
        
        this.updateSelectedItems();
        this.updateInput();
        this.updateGenerateButton();
    }
    
    updateSelectedItems() {
        const selectedItemsContainer = this.container.querySelector('.selected-items');
        selectedItemsContainer.innerHTML = '';
        
        this.selectedVehicles.forEach(vehicleId => {
            const vehicle = this.allVehicles.find(v => v.id === vehicleId);
            if (vehicle) {
                const item = document.createElement('span');
                item.className = 'selected-item';
                item.innerHTML = `
                    ${vehicle.name}
                    <span class="remove-btn" data-vehicle-id="${vehicleId}">×</span>
                `;
                
                item.querySelector('.remove-btn').addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.removeVehicle(vehicleId);
                });
                
                selectedItemsContainer.appendChild(item);
            }
        });
    }
    
    updateInput() {
        const input = this.container.querySelector('.multiselect-input');
        if (this.selectedVehicles.length === 0) {
            input.value = '';
            input.placeholder = '点击选择车型...';
        } else {
            input.value = `已选择 ${this.selectedVehicles.length} 个车型`;
        }
    }
    
    updateGenerateButton() {
        const generateBtn = document.getElementById('generate-comparison-btn');
        if (generateBtn) {
            generateBtn.disabled = this.selectedVehicles.length === 0;
        }
    }
    
    removeVehicle(vehicleId) {
        const index = this.selectedVehicles.indexOf(vehicleId);
        if (index > -1) {
            this.selectedVehicles.splice(index, 1);
            this.updateSelectedItems();
            this.updateInput();
            this.updateGenerateButton();
            this.renderOptions();
        }
    }
    
    filterOptions(searchTerm) {
        const options = this.container.querySelectorAll('.multiselect-option');
        options.forEach(option => {
            const text = option.textContent.toLowerCase();
            const matches = text.includes(searchTerm.toLowerCase());
            option.style.display = matches ? 'flex' : 'none';
        });
    }
    
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    open() {
        this.container.querySelector('.multiselect-container').classList.add('open');
        this.isOpen = true;
    }
    
    close() {
        this.container.querySelector('.multiselect-container').classList.remove('open');
        this.isOpen = false;
    }
    
    getSelectedVehicles() {
        return this.selectedVehicles;
    }
}

// 气密性对比管理器
class AirtightnessComparisonManager {
    constructor() {
        this.multiSelect = null;
        this.init();
    }
    
    init() {
        this.multiSelect = new VehicleMultiSelect('vehicle-multiselect');
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        const generateBtn = document.getElementById('generate-comparison-btn');
        const exportBtn = document.getElementById('export-btn');
        
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateComparison();
            });
        }
        
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportData();
            });
        }
    }
    
    async generateComparison() {
        const selectedVehicles = this.multiSelect.getSelectedVehicles();
        
        if (selectedVehicles.length === 0) {
            utils.showMessage('请至少选择一个车型', 'warning');
            return;
        }
        
        this.showLoading();
        
        try {
            const response = await utils.request('/airtightness/api/comparison', {
                method: 'POST',
                body: JSON.stringify({
                    vehicle_ids: selectedVehicles
                })
            });
            
            this.displayResults(response.data);
            utils.showMessage('对比数据生成成功', 'success');
        } catch (error) {
            utils.showMessage('生成对比数据失败: ' + error.message, 'danger');
        } finally {
            this.hideLoading();
        }
    }
    
    displayResults(data) {
        // 隐藏空状态，显示结果
        document.getElementById('empty-state').style.display = 'none';
        document.getElementById('results-card').style.display = 'block';
        
        // 更新车型数量
        document.getElementById('vehicle-count').textContent = `${data.vehicles.length} 个车型`;
        
        // 生成对比表格
        this.generateComparisonTable(data);
        
        // 生成测试信息表格
        this.generateTestInfoTable(data.vehicles);
    }
    
    generateComparisonTable(data) {
        const table = document.getElementById('comparison-table');
        const thead = table.querySelector('thead');
        const tbody = table.querySelector('tbody');
        
        // 清空现有内容
        thead.innerHTML = '';
        tbody.innerHTML = '';
        
        // 生成表头
        const headerRow = document.createElement('tr');
        headerRow.innerHTML = '<th>测试区域</th><th>测试项目</th>';
        data.vehicles.forEach(vehicle => {
            headerRow.innerHTML += `<th>${vehicle.name}</th>`;
        });
        thead.appendChild(headerRow);
        
        // 生成表格内容
        if (data.areas && data.areas.length > 0) {
            data.areas.forEach(area => {
                area.items.forEach((item, index) => {
                    const row = document.createElement('tr');

                    if (index === 0) {
                        const categoryCell = document.createElement('td');
                        categoryCell.className = 'category-cell';
                        categoryCell.textContent = area.category;
                        categoryCell.rowSpan = area.items.length;
                        row.appendChild(categoryCell);
                    }

                    // 添加项目名称列
                    const itemCell = document.createElement('td');
                    itemCell.className = 'item-cell';
                    itemCell.textContent = item.name || '-';
                    row.appendChild(itemCell);

                    // 添加数值列
                    item.values.forEach(value => {
                        const cell = document.createElement('td');
                        cell.className = 'value-cell';
                        cell.textContent = value !== null && value !== undefined ? value : '-';
                        if (value === null || value === undefined) {
                            cell.classList.add('no-data');
                        }
                        row.appendChild(cell);
                    });

                    tbody.appendChild(row);
                });
            });
        } else {
            const row = document.createElement('tr');
            row.innerHTML = `<td colspan="${data.vehicles.length + 1}" class="text-center text-muted">暂无对比数据</td>`;
            tbody.appendChild(row);
        }
    }
    
    generateTestInfoTable(vehicles) {
        const tbody = document.querySelector('#test-info-table tbody');
        tbody.innerHTML = '';
        
        vehicles.forEach(vehicle => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${vehicle.name}</td>
                <td>${vehicle.test_date || '-'}</td>
                <td>${vehicle.test_engineer || '-'}</td>
                <td>${vehicle.test_location || '-'}</td>
            `;
            tbody.appendChild(row);
        });
    }
    
    async exportData() {
        const selectedVehicles = this.multiSelect.getSelectedVehicles();
        
        if (selectedVehicles.length === 0) {
            utils.showMessage('请先生成对比数据', 'warning');
            return;
        }
        
        try {
            const response = await fetch('/airtightness/api/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    vehicle_ids: selectedVehicles
                })
            });
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'airtightness_comparison.csv';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                utils.showMessage('数据导出成功', 'success');
            } else {
                throw new Error('导出失败');
            }
        } catch (error) {
            utils.showMessage('导出数据失败: ' + error.message, 'danger');
        }
    }
    
    showLoading() {
        document.getElementById('loading-indicator').style.display = 'block';
        document.getElementById('empty-state').style.display = 'none';
        document.getElementById('results-card').style.display = 'none';
    }
    
    hideLoading() {
        document.getElementById('loading-indicator').style.display = 'none';
    }
}

// 气密性图片管理器
class AirtightnessImagesManager {
    constructor() {
        this.selectedVehicle = null;
        this.init();
    }

    init() {
        this.loadVehicles();
        this.setupEventListeners();
    }

    setupEventListeners() {
        const vehicleSelect = document.getElementById('vehicle-select');
        const viewImagesBtn = document.getElementById('view-images-btn');
        const openNewWindowBtn = document.getElementById('open-new-window-btn');

        if (vehicleSelect) {
            vehicleSelect.addEventListener('change', (e) => {
                this.selectedVehicle = e.target.value;
                this.updateButtons();
            });
        }

        if (viewImagesBtn) {
            viewImagesBtn.addEventListener('click', () => {
                this.viewImages();
            });
        }

        if (openNewWindowBtn) {
            openNewWindowBtn.addEventListener('click', () => {
                this.openInNewWindow();
            });
        }
    }

    async loadVehicles() {
        try {
            const response = await utils.request('/airtightness/api/all-vehicles');
            const vehicles = response.data || [];

            const select = document.getElementById('vehicle-select');
            if (select) {
                select.innerHTML = '<option value="">请选择车型...</option>';
                vehicles.forEach(vehicle => {
                    const option = document.createElement('option');
                    option.value = vehicle.id;
                    option.textContent = vehicle.name;
                    select.appendChild(option);
                });
            }
        } catch (error) {
            utils.showMessage('加载车型列表失败: ' + error.message, 'danger');
        }
    }

    updateButtons() {
        const viewImagesBtn = document.getElementById('view-images-btn');
        const openNewWindowBtn = document.getElementById('open-new-window-btn');

        const hasSelection = this.selectedVehicle && this.selectedVehicle !== '';

        if (viewImagesBtn) {
            viewImagesBtn.disabled = !hasSelection;
        }

        if (openNewWindowBtn) {
            openNewWindowBtn.disabled = !hasSelection;
        }
    }

    async viewImages() {
        if (!this.selectedVehicle) {
            utils.showMessage('请先选择车型', 'warning');
            return;
        }

        this.showLoading();

        try {
            const response = await utils.request(`/airtightness/api/images/${this.selectedVehicle}`);
            this.displayImages(response.data);
            utils.showMessage('图片加载成功', 'success');
        } catch (error) {
            utils.showMessage('加载图片失败: ' + error.message, 'danger');
        } finally {
            this.hideLoading();
        }
    }

    displayImages(data) {
        // 隐藏空状态，显示图片容器
        document.getElementById('empty-state').style.display = 'none';
        document.getElementById('images-container').style.display = 'block';

        // 显示各部位图片
        this.displayCategoryImages('front-compartment-container', data.front_compartment || []);
        this.displayCategoryImages('doors-container', data.doors || []);
        this.displayCategoryImages('tailgate-container', data.tailgate || []);
    }

    displayCategoryImages(containerId, images) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (images.length === 0) {
            container.innerHTML = `
                <div class="no-image-placeholder">
                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无图片</p>
                </div>
            `;
            return;
        }

        // 如果有多张图片，显示第一张，并添加切换功能
        const firstImage = images[0];
        container.innerHTML = `
            <img src="${firstImage.image_url}" alt="${firstImage.description || '测试图片'}"
                 class="img-fluid" onclick="previewImage('${firstImage.image_url}', '${firstImage.description || '测试图片'}')">
            ${images.length > 1 ? `<div class="image-count-badge">${images.length} 张图片</div>` : ''}
        `;

        // 如果有多张图片，添加点击切换功能
        if (images.length > 1) {
            let currentIndex = 0;
            const img = container.querySelector('img');

            img.addEventListener('click', (e) => {
                e.stopPropagation();
                currentIndex = (currentIndex + 1) % images.length;
                const currentImage = images[currentIndex];
                img.src = currentImage.image_url;
                img.alt = currentImage.description || '测试图片';
            });
        }
    }

    openInNewWindow() {
        if (!this.selectedVehicle) {
            utils.showMessage('请先选择车型', 'warning');
            return;
        }

        const url = `/airtightness/images/${this.selectedVehicle}`;
        window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
    }

    showLoading() {
        document.getElementById('loading-indicator').style.display = 'block';
        document.getElementById('empty-state').style.display = 'none';
        document.getElementById('images-container').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('loading-indicator').style.display = 'none';
    }
}

// 图片预览功能
function previewImage(imageUrl, title) {
    const modal = document.getElementById('image-preview-modal');
    const modalTitle = document.getElementById('image-preview-title');
    const previewImage = document.getElementById('preview-image');

    if (modal && modalTitle && previewImage) {
        modalTitle.textContent = title;
        previewImage.src = imageUrl;
        previewImage.alt = title;

        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 根据页面类型初始化相应的管理器
    if (document.getElementById('vehicle-multiselect')) {
        // 气密性对比页面
        new AirtightnessComparisonManager();
    } else if (document.getElementById('vehicle-select')) {
        // 气密性图片页面
        new AirtightnessImagesManager();
    }
});

} // 结束防重复加载检查
