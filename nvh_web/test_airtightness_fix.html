<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气密性功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
        .test-result {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 0.375rem;
        }
        .test-success {
            background-color: #d1edff;
            border: 1px solid #0ea5e9;
            color: #0369a1;
        }
        .test-error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #dc2626;
        }
        .api-response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">气密性功能测试页面</h1>
        
        <!-- API测试区域 -->
        <div class="test-section">
            <h3><i class="fas fa-cog me-2"></i>API端点测试</h3>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary mb-2" onclick="testVehiclesAPI()">
                        <i class="fas fa-car me-1"></i>测试车型列表API
                    </button>
                    <div id="vehicles-result" class="test-result" style="display: none;"></div>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-primary mb-2" onclick="testAllVehiclesAPI()">
                        <i class="fas fa-list me-1"></i>测试所有车型API
                    </button>
                    <div id="all-vehicles-result" class="test-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- 对比功能测试 -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar me-2"></i>对比功能测试</h3>
            <div class="mb-3">
                <label class="form-label">选择车型进行对比测试：</label>
                <select multiple class="form-select" id="comparison-vehicles" size="3">
                    <!-- 动态加载 -->
                </select>
            </div>
            <button class="btn btn-success" onclick="testComparisonAPI()">
                <i class="fas fa-play me-1"></i>测试对比功能
            </button>
            <div id="comparison-result" class="test-result" style="display: none;"></div>
        </div>

        <!-- 图片功能测试 -->
        <div class="test-section">
            <h3><i class="fas fa-images me-2"></i>图片功能测试</h3>
            <div class="mb-3">
                <label class="form-label">选择车型查看图片：</label>
                <select class="form-select" id="image-vehicle">
                    <option value="">请选择车型...</option>
                    <!-- 动态加载 -->
                </select>
            </div>
            <button class="btn btn-info" onclick="testImagesAPI()">
                <i class="fas fa-play me-1"></i>测试图片功能
            </button>
            <div id="images-result" class="test-result" style="display: none;"></div>
        </div>

        <!-- 页面链接测试 -->
        <div class="test-section">
            <h3><i class="fas fa-link me-2"></i>页面访问测试</h3>
            <div class="d-flex gap-2 flex-wrap">
                <a href="/airtightness/comparison" class="btn btn-outline-primary" target="_blank">
                    <i class="fas fa-chart-bar me-1"></i>气密性对比页面
                </a>
                <a href="/airtightness/comparison_content" class="btn btn-outline-primary" target="_blank">
                    <i class="fas fa-chart-bar me-1"></i>对比内容页面
                </a>
                <a href="/airtightness/images" class="btn btn-outline-info" target="_blank">
                    <i class="fas fa-images me-1"></i>气密性图片页面
                </a>
                <a href="/airtightness/images_content" class="btn btn-outline-info" target="_blank">
                    <i class="fas fa-images me-1"></i>图片内容页面
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 工具函数
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw error;
            }
        }

        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `test-result ${success ? 'test-success' : 'test-error'}`;
            
            let content = `<strong>${success ? '✅ 成功' : '❌ 失败'}:</strong> ${message}`;
            if (data) {
                content += `<div class="api-response mt-2">${JSON.stringify(data, null, 2)}</div>`;
            }
            element.innerHTML = content;
        }

        // 测试车型列表API
        async function testVehiclesAPI() {
            try {
                const data = await makeRequest('/airtightness/api/vehicles');
                showResult('vehicles-result', true, '车型列表API调用成功', data);
                
                // 填充对比测试的选择框
                const select = document.getElementById('comparison-vehicles');
                select.innerHTML = '';
                data.data.forEach(vehicle => {
                    const option = document.createElement('option');
                    option.value = vehicle.id;
                    option.textContent = vehicle.name;
                    select.appendChild(option);
                });
            } catch (error) {
                showResult('vehicles-result', false, error.message);
            }
        }

        // 测试所有车型API
        async function testAllVehiclesAPI() {
            try {
                const data = await makeRequest('/airtightness/api/all-vehicles');
                showResult('all-vehicles-result', true, '所有车型API调用成功', data);
                
                // 填充图片测试的选择框
                const select = document.getElementById('image-vehicle');
                select.innerHTML = '<option value="">请选择车型...</option>';
                data.data.forEach(vehicle => {
                    const option = document.createElement('option');
                    option.value = vehicle.id;
                    option.textContent = vehicle.name;
                    select.appendChild(option);
                });
            } catch (error) {
                showResult('all-vehicles-result', false, error.message);
            }
        }

        // 测试对比API
        async function testComparisonAPI() {
            const select = document.getElementById('comparison-vehicles');
            const selectedOptions = Array.from(select.selectedOptions);
            
            if (selectedOptions.length === 0) {
                showResult('comparison-result', false, '请先选择至少一个车型');
                return;
            }
            
            const vehicleIds = selectedOptions.map(option => parseInt(option.value));
            
            try {
                const data = await makeRequest('/airtightness/api/comparison', {
                    method: 'POST',
                    body: JSON.stringify({ vehicle_ids: vehicleIds })
                });
                showResult('comparison-result', true, '对比数据生成成功', data);
            } catch (error) {
                showResult('comparison-result', false, error.message);
            }
        }

        // 测试图片API
        async function testImagesAPI() {
            const select = document.getElementById('image-vehicle');
            const vehicleId = select.value;
            
            if (!vehicleId) {
                showResult('images-result', false, '请先选择一个车型');
                return;
            }
            
            try {
                const data = await makeRequest(`/airtightness/api/images/${vehicleId}`);
                showResult('images-result', true, '图片数据获取成功', data);
            } catch (error) {
                showResult('images-result', false, error.message);
            }
        }

        // 页面加载时自动测试API
        window.addEventListener('DOMContentLoaded', function() {
            testVehiclesAPI();
            testAllVehiclesAPI();
        });
    </script>
</body>
</html>
