<!-- 业务中心内容 - 用于标签页显示 -->
<style>
.business-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.business-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.business-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.search-box {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.module-section {
    margin-bottom: 3rem;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.coming-soon-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #ffc107;
    color: #212529;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}
</style>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-briefcase me-2 text-primary"></i>业务中心
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-filter me-1"></i>筛选
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-sort me-1"></i>排序
            </button>
        </div>
    </div>
</div>

<!-- 搜索框 -->
<div class="search-box">
    <div class="row align-items-center">
        <div class="col-md-8">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="搜索业务功能..." id="businessSearch">
            </div>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-primary" onclick="searchBusiness()">
                <i class="fas fa-search me-1"></i>搜索
            </button>
            <button class="btn btn-outline-secondary ms-2" onclick="clearSearch()">
                <i class="fas fa-times me-1"></i>清除
            </button>
        </div>
    </div>
</div>

<!-- 模态模块 -->
<div class="module-section">
    <h3 class="section-title">
        <i class="fas fa-wave-square me-2"></i>模态模块
    </h3>
    <div class="row">
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100">
                <div class="card-body text-center">
                    <div class="business-icon bg-primary">
                        <i class="fas fa-search"></i>
                    </div>
                    <h5 class="card-title">模态数据查询</h5>
                    <p class="card-text text-muted">查询和分析车辆及零部件的模态测试数据</p>
                    <button class="btn btn-primary" onclick="openModalSearch()">进入查询</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100 position-relative">
                <span class="coming-soon-badge">敬请期待</span>
                <div class="card-body text-center">
                    <div class="business-icon bg-info">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h5 class="card-title">模态分析</h5>
                    <p class="card-text text-muted">深度分析模态数据，生成专业报告</p>
                    <button class="btn btn-info" onclick="showComingSoon()">敬请期待</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100 position-relative">
                <span class="coming-soon-badge">敬请期待</span>
                <div class="card-body text-center">
                    <div class="business-icon bg-success">
                        <i class="fas fa-file-export"></i>
                    </div>
                    <h5 class="card-title">模态报告</h5>
                    <p class="card-text text-muted">自动生成模态测试分析报告</p>
                    <button class="btn btn-success" onclick="showComingSoon()">敬请期待</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 吸隔声模块 -->
<div class="module-section">
    <h3 class="section-title">
        <i class="fas fa-volume-up me-2"></i>吸隔声模块
    </h3>
    <div class="row">
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100">
                <div class="card-body text-center">
                    <div class="business-icon bg-warning">
                        <i class="fas fa-chart-area"></i>
                    </div>
                    <h5 class="card-title">区域隔声量</h5>
                    <p class="card-text text-muted">区域隔声量ATF对比分析和测试图片查看</p>
                    <button class="btn btn-warning" onclick="openSoundInsulation()">进入查询</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100">
                <div class="card-body text-center">
                    <div class="business-icon bg-success">
                        <i class="fas fa-music"></i>
                    </div>
                    <h5 class="card-title">吸音系数</h5>
                    <p class="card-text text-muted">垂直入射法吸音系数查询和分析</p>
                    <button class="btn btn-success" onclick="openSoundAbsorption()">进入查询</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100">
                <div class="card-body text-center">
                    <div class="business-icon bg-info">
                        <i class="fas fa-broadcast-tower"></i>
                    </div>
                    <h5 class="card-title">传声损失</h5>
                    <p class="card-text text-muted">材料传声损失测试数据查询</p>
                    <button class="btn btn-info" onclick="openSoundTransmission()">进入查询</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100">
                <div class="card-body text-center">
                    <div class="business-icon bg-secondary">
                        <i class="fas fa-wall-brick"></i>
                    </div>
                    <h5 class="card-title">壁挂传声</h5>
                    <p class="card-text text-muted">壁挂式传声损失测试数据管理</p>
                    <button class="btn btn-secondary" onclick="openWallMountedTransmission()">进入查询</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100">
                <div class="card-body text-center">
                    <div class="business-icon bg-dark">
                        <i class="fas fa-filter"></i>
                    </div>
                    <h5 class="card-title">材料孔隙率</h5>
                    <p class="card-text text-muted">材料孔隙率和流阻测试数据</p>
                    <button class="btn btn-dark" onclick="openMaterialPorosity()">进入查询</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 气密性模块 -->
<div class="module-section">
    <h3 class="section-title">
        <i class="fas fa-tachometer-alt me-2"></i>气密性模块
    </h3>
    <div class="row">
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100">
                <div class="card-body text-center">
                    <div class="business-icon bg-danger">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h5 class="card-title">气密性泄漏量对比</h5>
                    <p class="card-text text-muted">多车型气密性泄漏量数据对比分析</p>
                    <button class="btn btn-danger" onclick="openAirtightnessComparison()">进入查询</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100">
                <div class="card-body text-center">
                    <div class="business-icon bg-primary">
                        <i class="fas fa-images"></i>
                    </div>
                    <h5 class="card-title">气密性测试图片</h5>
                    <p class="card-text text-muted">气密性测试过程图片查看和管理</p>
                    <button class="btn btn-primary" onclick="openAirtightnessImages()">进入查看</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据管理模块 -->
<div class="module-section">
    <h3 class="section-title">
        <i class="fas fa-database me-2"></i>数据管理
    </h3>
    <div class="row">
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100 position-relative">
                <span class="coming-soon-badge">敬请期待</span>
                <div class="card-body text-center">
                    <div class="business-icon bg-info">
                        <i class="fas fa-upload"></i>
                    </div>
                    <h5 class="card-title">数据导入</h5>
                    <p class="card-text text-muted">批量导入测试数据和文件</p>
                    <button class="btn btn-info" onclick="showComingSoon()">敬请期待</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100 position-relative">
                <span class="coming-soon-badge">敬请期待</span>
                <div class="card-body text-center">
                    <div class="business-icon bg-success">
                        <i class="fas fa-download"></i>
                    </div>
                    <h5 class="card-title">数据导出</h5>
                    <p class="card-text text-muted">导出数据为各种格式文件</p>
                    <button class="btn btn-success" onclick="showComingSoon()">敬请期待</button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card business-card h-100 position-relative">
                <span class="coming-soon-badge">敬请期待</span>
                <div class="card-body text-center">
                    <div class="business-icon bg-warning">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h5 class="card-title">数据统计</h5>
                    <p class="card-text text-muted">数据统计分析和可视化展示</p>
                    <button class="btn btn-warning" onclick="showComingSoon()">敬请期待</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 搜索功能
function searchBusiness() {
    const searchTerm = document.getElementById('businessSearch').value.toLowerCase();
    const cards = document.querySelectorAll('.business-card');

    cards.forEach(card => {
        const title = card.querySelector('.card-title').textContent.toLowerCase();
        const text = card.querySelector('.card-text').textContent.toLowerCase();

        if (title.includes(searchTerm) || text.includes(searchTerm)) {
            card.parentElement.style.display = 'block';
        } else {
            card.parentElement.style.display = 'none';
        }
    });
}

function clearSearch() {
    document.getElementById('businessSearch').value = '';
    const cards = document.querySelectorAll('.business-card');
    cards.forEach(card => {
        card.parentElement.style.display = 'block';
    });
}

// 回车搜索
document.getElementById('businessSearch').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchBusiness();
    }
});

// 各功能模块打开函数
function openModalSearch() {
    if (window.parent && window.parent.tabManager) {
        window.parent.tabManager.openPage('/modal/search', '模态数据查询', 'fas fa-search');
    }
}

function openSoundInsulation() {
    if (window.parent && window.parent.tabManager) {
        window.parent.tabManager.openPage('/sound_insulation/area_comparison', '区域隔声量', 'fas fa-chart-area');
    }
}

function openSoundAbsorption() {
    if (window.parent && window.parent.tabManager) {
        window.parent.tabManager.openPage('/sound_absorption/coefficient_query', '吸音系数', 'fas fa-music');
    }
}

function openSoundTransmission() {
    if (window.parent && window.parent.tabManager) {
        window.parent.tabManager.openPage('/sound_transmission/transmission_loss_query', '传声损失', 'fas fa-broadcast-tower');
    }
}

function openWallMountedTransmission() {
    if (window.parent && window.parent.tabManager) {
        window.parent.tabManager.openPage('/wall_mounted_transmission/transmission_loss_query', '壁挂传声', 'fas fa-wall-brick');
    }
}

function openMaterialPorosity() {
    if (window.parent && window.parent.tabManager) {
        window.parent.tabManager.openPage('/material_porosity_flow_resistance/query', '材料孔隙率', 'fas fa-filter');
    }
}

function openAirtightnessComparison() {
    if (window.parent && window.parent.tabManager) {
        window.parent.tabManager.openPage('/airtightness/comparison', '气密性泄漏量对比', 'fas fa-chart-bar');
    }
}

function openAirtightnessImages() {
    if (window.parent && window.parent.tabManager) {
        window.parent.tabManager.openPage('/airtightness/images', '气密性测试图片', 'fas fa-images');
    }
}

function showComingSoon() {
    if (window.parent && window.parent.showMessage) {
        window.parent.showMessage('该功能正在开发中，敬请期待！', 'info');
    } else {
        alert('该功能正在开发中，敬请期待！');
    }
}
</script>
