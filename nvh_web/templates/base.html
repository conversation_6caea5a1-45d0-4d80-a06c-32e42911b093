<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}NVH数据管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/base.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/tabs.css') }}" rel="stylesheet">
    {% block head %}{% endblock %}
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            {% if session.user %}
            <button class="btn btn-outline-light me-3" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            {% endif %}
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2"></i>NVH数据管理系统
            </a>
            
            <div class="navbar-nav ms-auto">
                {% if session.user %}
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        {{ session.user_info.name or session.user_info.username or '用户' }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">用户信息</h6></li>
                        <li><span class="dropdown-item-text small">{{ session.user_info.email or '未设置邮箱' }}</span></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="showComingSoon()"><i class="fas fa-user-cog me-2"></i>个人设置</a></li>
                        <li><a class="dropdown-item" href="#" onclick="showComingSoon()"><i class="fas fa-bell me-2"></i>通知设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
                {% else %}
                <a class="nav-link text-white" href="/login">
                    <i class="fas fa-sign-in-alt me-1"></i>登录
                </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {% if session.user %}
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar" id="sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('index') }}">
                                <i class="fas fa-home me-2"></i>首页
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('business_center') }}">
                                <i class="fas fa-briefcase me-2"></i>业务中心
                            </a>
                        </li>

                        <!-- 待开发模块 -->
                        <li class="nav-item">
                            <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#developmentCollapse">
                                <i class="fas fa-code me-2"></i>待开发模块
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="developmentCollapse">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showComingSoon()">
                                            <i class="fas fa-users-cog me-2"></i>权限管理
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showComingSoon()">
                                            <i class="fas fa-chart-pie me-2"></i>数据分析
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showComingSoon()">
                                            <i class="fas fa-file-export me-2"></i>报告生成
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="showComingSoon()">
                                            <i class="fas fa-cog me-2"></i>系统设置
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </div>
            </nav>
            {% endif %}

            <!-- 主内容区域 -->
            <main class="{% if session.user %}col-md-9 ms-sm-auto col-lg-10{% else %}col-12{% endif %} px-md-4" id="mainContent">
                {% if not session.user %}
                {% block content %}{% endblock %}
                {% endif %}
                <!-- 登录后的内容由标签页系统管理 -->
            </main>
        </div>
    </div>

    <!-- Toast容器 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/request.js') }}"></script>
    <script src="{{ url_for('static', filename='js/base.js') }}"></script>
    {% if session.user %}
    <script src="{{ url_for('static', filename='js/tabs.js') }}"></script>
    {% endif %}
    {% block extra_js %}{% endblock %}
</body>
</html>
