#!/usr/bin/env python3
"""
NVH数据管理系统修复验证脚本
用于测试URL路由和业务中心功能是否正常工作
"""

import requests
import time
import sys
from urllib.parse import urljoin

def test_server_endpoints(base_url="http://127.0.0.1:5000"):
    """测试服务器端点"""
    print("🔍 测试服务器端点...")
    
    endpoints = [
        "/",
        "/business_center",
        "/.well-known/appspecific/com.chrome.devtools.json"
    ]
    
    results = {}
    
    for endpoint in endpoints:
        url = urljoin(base_url, endpoint)
        try:
            response = requests.get(url, timeout=5)
            results[endpoint] = {
                'status_code': response.status_code,
                'success': response.status_code in [200, 404],  # 404 is expected for devtools
                'content_length': len(response.content)
            }
            print(f"  ✓ {endpoint}: {response.status_code} ({len(response.content)} bytes)")
        except requests.exceptions.RequestException as e:
            results[endpoint] = {
                'status_code': None,
                'success': False,
                'error': str(e)
            }
            print(f"  ✗ {endpoint}: {e}")
    
    return results

def test_static_files(base_url="http://127.0.0.1:5000"):
    """测试静态文件"""
    print("\n📁 测试静态文件...")
    
    static_files = [
        "/static/css/base.css",
        "/static/js/base.js",
        "/static/js/tabs.js",
        "/static/js/request.js"
    ]
    
    results = {}
    
    for file_path in static_files:
        url = urljoin(base_url, file_path)
        try:
            response = requests.get(url, timeout=5)
            results[file_path] = {
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'content_length': len(response.content)
            }
            print(f"  ✓ {file_path}: {response.status_code} ({len(response.content)} bytes)")
        except requests.exceptions.RequestException as e:
            results[file_path] = {
                'status_code': None,
                'success': False,
                'error': str(e)
            }
            print(f"  ✗ {file_path}: {e}")
    
    return results

def test_error_pages(base_url="http://127.0.0.1:5000"):
    """测试错误页面"""
    print("\n🚫 测试错误页面...")
    
    # 测试404页面
    try:
        response = requests.get(urljoin(base_url, "/nonexistent-page"), timeout=5)
        print(f"  ✓ 404页面: {response.status_code}")
        if "404" in response.text:
            print("    ✓ 404页面内容正确")
        else:
            print("    ⚠ 404页面内容可能有问题")
    except Exception as e:
        print(f"  ✗ 404页面测试失败: {e}")

def check_javascript_syntax():
    """检查JavaScript文件语法"""
    print("\n🔧 检查JavaScript文件...")
    
    js_files = [
        "nvh_web/static/js/tabs.js",
        "nvh_web/static/js/base.js"
    ]
    
    for js_file in js_files:
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 简单的语法检查
            if content.count('{') != content.count('}'):
                print(f"  ⚠ {js_file}: 大括号不匹配")
            elif content.count('(') != content.count(')'):
                print(f"  ⚠ {js_file}: 小括号不匹配")
            else:
                print(f"  ✓ {js_file}: 语法检查通过")
                
        except FileNotFoundError:
            print(f"  ✗ {js_file}: 文件不存在")
        except Exception as e:
            print(f"  ✗ {js_file}: {e}")

def generate_test_report(endpoint_results, static_results):
    """生成测试报告"""
    print("\n📊 测试报告")
    print("=" * 50)
    
    total_tests = len(endpoint_results) + len(static_results)
    passed_tests = sum(1 for r in endpoint_results.values() if r['success']) + \
                   sum(1 for r in static_results.values() if r['success'])
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！服务器运行正常。")
        return True
    else:
        print("\n⚠ 部分测试失败，请检查服务器配置。")
        return False

def main():
    """主函数"""
    print("NVH数据管理系统修复验证")
    print("=" * 40)
    
    # 检查JavaScript语法
    check_javascript_syntax()
    
    # 测试服务器（如果运行的话）
    try:
        endpoint_results = test_server_endpoints()
        static_results = test_static_files()
        test_error_pages()
        
        success = generate_test_report(endpoint_results, static_results)
        
        if success:
            print("\n✅ 修复验证完成，系统应该正常工作。")
            print("\n📝 测试步骤:")
            print("1. 访问 http://127.0.0.1:5000")
            print("2. 点击左侧菜单'业务中心'")
            print("3. 检查URL是否更新为 /business_center")
            print("4. 检查业务中心内容是否正确显示")
        else:
            print("\n❌ 发现问题，请检查服务器日志。")
            
    except requests.exceptions.ConnectionError:
        print("\n⚠ 无法连接到服务器，请确保Flask应用正在运行。")
        print("启动命令: cd nvh_web && python app.py")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
