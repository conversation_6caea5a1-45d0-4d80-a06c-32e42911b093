import os
from flask import Flask, render_template, session, redirect, url_for, jsonify
from authlib.integrations.flask_client import OAuth
from config import Config
from models import db
from controllers.auth_controller import auth_bp
from controllers.modal_controller import modal_bp
from controllers.airtightness_controller import airtightness_bp
from controllers.sound_insulation_controller import sound_insulation_bp
from controllers.sound_absorption_controller import sound_absorption_bp
from controllers.sound_transmission_controller import sound_transmission_bp
from controllers.wall_mounted_transmission_controller import wall_mounted_transmission_bp
from controllers.material_porosity_flow_resistance_controller import material_porosity_flow_resistance_bp
from decorators import login_required

# 创建Flask应用
app = Flask(__name__)
app.config.from_object(Config)

# 初始化数据库
db.init_app(app)

# 初始化OAuth
oauth = OAuth(app)

# 注册Keycloak客户端
keycloak = oauth.register(
    name='keycloak',
    client_id=app.config['KEYCLOAK_FRONTEND_CLIENT_ID'],
    client_secret=app.config['KEYCLOAK_FRONTEND_CLIENT_SECRET'],
    server_metadata_url=app.config['KEYCLOAK_SERVER_METADATA_URL'],
    client_kwargs=app.config['KEYCLOAK_CLIENT_KWARGS'],
)

# 注册蓝图
app.register_blueprint(auth_bp)
app.register_blueprint(modal_bp)
app.register_blueprint(airtightness_bp)
app.register_blueprint(sound_insulation_bp)
app.register_blueprint(sound_absorption_bp)
app.register_blueprint(sound_transmission_bp)
app.register_blueprint(wall_mounted_transmission_bp)
app.register_blueprint(material_porosity_flow_resistance_bp)

# ========== 页面路由 ==========
@app.route('/')
@login_required
def index():
    """主页 - 需要认证"""
    return render_template('index.html')

@app.route('/business_center')
@login_required
def business_center():
    """业务中心页面"""
    return render_template('business_center.html')

@app.route('/business_center_content')
@login_required
def business_center_content():
    """业务中心内容 - 用于标签页显示"""
    return render_template('business_center_content.html')

@app.route('/login')
def login():
    """登录"""
    if 'user' in session:
        return redirect(url_for('index'))

    redirect_uri = url_for('auth_callback', _external=True)
    return keycloak.authorize_redirect(redirect_uri)

@app.route('/auth/callback')
def auth_callback():
    """认证回调"""
    try:
        token = keycloak.authorize_access_token()
        user = token.get('userinfo')
        if user:
            session['user'] = user
            session['token'] = token
            session['user_info'] = {
                'id': user.get('sub'),
                'username': user.get('preferred_username'),
                'email': user.get('email'),
                'name': user.get('name'),
                'roles': user.get('realm_access', {}).get('roles', [])
            }
        return redirect(url_for('index'))
    except Exception as e:
        print(f"认证回调错误: {e}")
        return redirect(url_for('login'))

@app.route('/logout')
def logout():
    """退出登录"""
    session.clear()
    return redirect(url_for('index'))

@app.route('/test-airtightness')
def test_airtightness():
    """气密性功能测试页面 - 无需认证"""
    with open('test_airtightness_fix.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/test')
def test_page():
    """测试页面 - 无需认证"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>系统测试</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body text-center">
                            <h2 class="text-success mb-4">✅ 系统运行正常</h2>
                            <p class="mb-4">NVH数据管理系统已成功启动，多标签页架构重构完成</p>

                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="card border-success">
                                        <div class="card-body">
                                            <i class="fas fa-home fa-2x text-success mb-2"></i>
                                            <h6>首页</h6>
                                            <small class="text-muted">✅ 已完成</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-success">
                                        <div class="card-body">
                                            <i class="fas fa-briefcase fa-2x text-success mb-2"></i>
                                            <h6>业务中心</h6>
                                            <small class="text-muted">✅ 已完成</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-success">
                                        <div class="card-body">
                                            <i class="fas fa-tachometer-alt fa-2x text-success mb-2"></i>
                                            <h6>气密性功能</h6>
                                            <small class="text-muted">✅ 已完成</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <a href="/" class="btn btn-primary">返回首页</a>
                                <a href="/business_center" class="btn btn-outline-primary">业务中心</a>
                                <a href="/airtightness/comparison_content" class="btn btn-outline-success">气密性对比</a>
                                <a href="/airtightness/images_content" class="btn btn-outline-info">气密性图片</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''




# 创建上传目录
upload_folder = app.config.get('UPLOAD_FOLDER')
if upload_folder and not os.path.exists(upload_folder):
    os.makedirs(upload_folder, exist_ok=True)
    os.makedirs(os.path.join(upload_folder, 'modal_shapes'), exist_ok=True)
    os.makedirs(os.path.join(upload_folder, 'test_photos'), exist_ok=True)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)  # 使用不同端口避免冲突
